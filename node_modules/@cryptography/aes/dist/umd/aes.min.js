!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).aes={})}(this,(function(e){"use strict";var t=new Uint8Array(256),n=new Uint8Array(256),r=new Uint32Array(256),i=new Uint32Array(256),h=new Uint32Array(256),o=new Uint32Array(256),c=new Uint32Array(256),s=new Uint32Array(256),y=new Uint32Array(256),a=new Uint32Array(256);function f(e){if(e instanceof Uint32Array)return e;if("string"==typeof e){if(e.length%4!=0)for(var t=e.length%4;t<=4;t++)e+="\0x00";var n=new Uint32Array(e.length/4);for(t=0;t<e.length;t+=4)n[t/4]=(i=t,(r=e).charCodeAt(i)<<24^r.charCodeAt(i+1)<<16^r.charCodeAt(i+2)<<8^r.charCodeAt(i+3));return n}var r,i;if(e instanceof Uint8Array){for(n=new Uint32Array(e.length/4),t=0;t<e.length;t+=4)n[t/4]=e[t]<<24^e[t+1]<<16^e[t+2]<<8^e[t+3];return n}throw new Error("Unable to create 32-bit words")}function l(e,t,n){void 0===n&&(n=e);for(var r=0;r<e.length;r++)n[r]=e[r]^t[r]}!function(){for(var e,f,l,u,g,p=new Uint8Array(256),d=new Uint8Array(256),v=0,w=0,A=0;A<256;A++)p[A]=A<<1^283*(A>>7),d[p[A]^A]=A;for(;!t[v];v^=e||1)l=(l=w^w<<1^w<<2^w<<3^w<<4)>>8^255&l^99,t[v]=l,n[l]=v,g=16843009*p[f=p[e=p[v]]]^65537*f^257*e^16843008*v,u=257*p[l]^16843008*l,r[v]=u=u<<24^u>>>8,i[v]=u=u<<24^u>>>8,h[v]=u=u<<24^u>>>8,o[v]=u=u<<24^u>>>8,c[l]=g=g<<24^g>>>8,s[l]=g=g<<24^g>>>8,y[l]=g=g<<24^g>>>8,a[l]=g=g<<24^g>>>8,w=d[w]||1}();var u=function(){function e(e){var n=f(e);if(4!==n.length&&6!==n.length&&8!==n.length)throw new Error("Invalid key size");this.encKey=new Uint32Array(4*n.length+28),this.decKey=new Uint32Array(4*n.length+28),this.encKey.set(n);for(var r,i=1,h=n.length;h<4*n.length+28;h++)r=this.encKey[h-1],(h%n.length==0||8===n.length&&h%n.length==4)&&(r=t[r>>>24]<<24^t[r>>16&255]<<16^t[r>>8&255]<<8^t[255&r],h%n.length==0&&(r=r<<8^r>>>24^i<<24,i=i<<1^283*(i>>7))),this.encKey[h]=this.encKey[h-n.length]^r;for(var o=0;h;o++,h--)r=this.encKey[3&o?h:h-4],this.decKey[o]=h<=4||o<4?r:c[t[r>>>24]]^s[t[r>>16&255]]^y[t[r>>8&255]]^a[t[255&r]]}return e.prototype.encrypt=function(e){for(var n,c,s,y=f(e),a=new Uint32Array(4),l=y[0]^this.encKey[0],u=y[1]^this.encKey[1],g=y[2]^this.encKey[2],p=y[3]^this.encKey[3],d=this.encKey.length/4-2,v=4,w=0;w<d;w++)n=r[l>>>24]^i[u>>16&255]^h[g>>8&255]^o[255&p]^this.encKey[v],c=r[u>>>24]^i[g>>16&255]^h[p>>8&255]^o[255&l]^this.encKey[v+1],s=r[g>>>24]^i[p>>16&255]^h[l>>8&255]^o[255&u]^this.encKey[v+2],p=r[p>>>24]^i[l>>16&255]^h[u>>8&255]^o[255&g]^this.encKey[v+3],l=n,u=c,g=s,v+=4;for(w=0;w<4;w++)a[w]=t[l>>>24]<<24^t[u>>16&255]<<16^t[g>>8&255]<<8^t[255&p]^this.encKey[v++],n=l,l=u,u=g,g=p,p=n;return a},e.prototype.decrypt=function(e){for(var t,r,i,h=f(e),o=new Uint32Array(4),l=h[0]^this.decKey[0],u=h[3]^this.decKey[1],g=h[2]^this.decKey[2],p=h[1]^this.decKey[3],d=this.decKey.length/4-2,v=4,w=0;w<d;w++)t=c[l>>>24]^s[u>>16&255]^y[g>>8&255]^a[255&p]^this.decKey[v],r=c[u>>>24]^s[g>>16&255]^y[p>>8&255]^a[255&l]^this.decKey[v+1],i=c[g>>>24]^s[p>>16&255]^y[l>>8&255]^a[255&u]^this.decKey[v+2],p=c[p>>>24]^s[l>>16&255]^y[u>>8&255]^a[255&g]^this.decKey[v+3],l=t,u=r,g=i,v+=4;for(w=0;w<4;w++)o[3&-w]=n[l>>>24]<<24^n[u>>16&255]<<16^n[g>>8&255]<<8^n[255&p]^this.decKey[v++],t=l,l=u,u=g,g=p,p=t;return o},e}(),g=function(){function e(e,t,n){void 0===n&&(n=16),this.key=f(e),this.iv=f(t),this.cipher=new u(e),this.blockSize=n/4}return e.prototype.encrypt=function(e,t){for(var n=f(e),r=t||new Uint32Array(n.length),i=this.iv.subarray(this.blockSize,this.iv.length),h=this.iv.subarray(0,this.blockSize),o=new Uint32Array(this.blockSize),c=0;c<n.length;c+=this.blockSize){var s=n.subarray(c,c+this.blockSize);l(s,h,o);var y=this.cipher.encrypt(o);l(y,i),i=s,h=y;for(var a=c,u=0;a<n.length&&u<4;a++,u++)r[a]=y[u]}return r},e.prototype.decrypt=function(e,t){for(var n=f(e),r=t||new Uint32Array(n.length),i=this.iv.subarray(this.blockSize,this.iv.length),h=this.iv.subarray(0,this.blockSize),o=new Uint32Array(this.blockSize),c=0;c<r.length;c+=this.blockSize){var s=n.subarray(c,c+this.blockSize);l(s,i,o);var y=this.cipher.decrypt(o);l(y,h),h=s,i=y;for(var a=c,u=0;a<r.length&&u<4;a++,u++)r[a]=y[u]}return r},e}(),p=function(){function e(e,t,n){if(void 0===n&&(n=16),this.offset=0,this.key=f(e),this.counter=f(t),this.cipher=new u(e),this.blockSize=n/4,4!==this.counter.length)throw new Error("AES-CTR mode counter must be 16 bytes length")}return e.prototype.encrypt=function(e,t){for(var n=f(e),r=t||new Uint32Array(n.length),i=this.offset,h=0;h<n.length;h+=this.blockSize){for(var o=this.cipher.encrypt(this.counter),c=h,s=i;c<n.length&&s<this.blockSize;c++,s++)r[c]=o[s]^n[c];n.length-h>=this.blockSize&&this.incrementCounter(),i&&(h-=i,i=0)}return this.offset=(this.offset+n.length%4)%4,r},e.prototype.decrypt=function(e,t){return this.encrypt(e,t)},e.prototype.incrementCounter=function(){for(var e=this.counter.length-1;e>=0&&!(++this.counter[e]<4294967295);e--);},e}();e.CTR=p,e.IGE=g,e.default=u,Object.defineProperty(e,"__esModule",{value:!0})}));
