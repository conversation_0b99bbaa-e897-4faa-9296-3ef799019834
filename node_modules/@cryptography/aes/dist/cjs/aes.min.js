"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var t=new Uint8Array(256),e=new Uint8Array(256),r=new Uint32Array(256),n=new Uint32Array(256),i=new Uint32Array(256),h=new Uint32Array(256),o=new Uint32Array(256),s=new Uint32Array(256),c=new Uint32Array(256),y=new Uint32Array(256);function a(t){if(t instanceof Uint32Array)return t;if("string"==typeof t){if(t.length%4!=0)for(var e=t.length%4;e<=4;e++)t+="\0x00";var r=new Uint32Array(t.length/4);for(e=0;e<t.length;e+=4)r[e/4]=(i=e,(n=t).charCodeAt(i)<<24^n.charCodeAt(i+1)<<16^n.charCodeAt(i+2)<<8^n.charCodeAt(i+3));return r}var n,i;if(t instanceof Uint8Array){for(r=new Uint32Array(t.length/4),e=0;e<t.length;e+=4)r[e/4]=t[e]<<24^t[e+1]<<16^t[e+2]<<8^t[e+3];return r}throw new Error("Unable to create 32-bit words")}function l(t,e,r){void 0===r&&(r=t);for(var n=0;n<t.length;n++)r[n]=t[n]^e[n]}!function(){for(var a,l,f,u,g,p=new Uint8Array(256),v=new Uint8Array(256),w=0,A=0,d=0;d<256;d++)p[d]=d<<1^283*(d>>7),v[p[d]^d]=d;for(;!t[w];w^=a||1)f=(f=A^A<<1^A<<2^A<<3^A<<4)>>8^255&f^99,t[w]=f,e[f]=w,g=16843009*p[l=p[a=p[w]]]^65537*l^257*a^16843008*w,u=257*p[f]^16843008*f,r[w]=u=u<<24^u>>>8,n[w]=u=u<<24^u>>>8,i[w]=u=u<<24^u>>>8,h[w]=u=u<<24^u>>>8,o[f]=g=g<<24^g>>>8,s[f]=g=g<<24^g>>>8,c[f]=g=g<<24^g>>>8,y[f]=g=g<<24^g>>>8,A=v[A]||1}();var f=function(){function l(e){var r=a(e);if(4!==r.length&&6!==r.length&&8!==r.length)throw new Error("Invalid key size");this.encKey=new Uint32Array(4*r.length+28),this.decKey=new Uint32Array(4*r.length+28),this.encKey.set(r);for(var n,i=1,h=r.length;h<4*r.length+28;h++)n=this.encKey[h-1],(h%r.length==0||8===r.length&&h%r.length==4)&&(n=t[n>>>24]<<24^t[n>>16&255]<<16^t[n>>8&255]<<8^t[255&n],h%r.length==0&&(n=n<<8^n>>>24^i<<24,i=i<<1^283*(i>>7))),this.encKey[h]=this.encKey[h-r.length]^n;for(var l=0;h;l++,h--)n=this.encKey[3&l?h:h-4],this.decKey[l]=h<=4||l<4?n:o[t[n>>>24]]^s[t[n>>16&255]]^c[t[n>>8&255]]^y[t[255&n]]}return l.prototype.encrypt=function(e){for(var o,s,c,y=a(e),l=new Uint32Array(4),f=y[0]^this.encKey[0],u=y[1]^this.encKey[1],g=y[2]^this.encKey[2],p=y[3]^this.encKey[3],v=this.encKey.length/4-2,w=4,A=0;A<v;A++)o=r[f>>>24]^n[u>>16&255]^i[g>>8&255]^h[255&p]^this.encKey[w],s=r[u>>>24]^n[g>>16&255]^i[p>>8&255]^h[255&f]^this.encKey[w+1],c=r[g>>>24]^n[p>>16&255]^i[f>>8&255]^h[255&u]^this.encKey[w+2],p=r[p>>>24]^n[f>>16&255]^i[u>>8&255]^h[255&g]^this.encKey[w+3],f=o,u=s,g=c,w+=4;for(A=0;A<4;A++)l[A]=t[f>>>24]<<24^t[u>>16&255]<<16^t[g>>8&255]<<8^t[255&p]^this.encKey[w++],o=f,f=u,u=g,g=p,p=o;return l},l.prototype.decrypt=function(t){for(var r,n,i,h=a(t),l=new Uint32Array(4),f=h[0]^this.decKey[0],u=h[3]^this.decKey[1],g=h[2]^this.decKey[2],p=h[1]^this.decKey[3],v=this.decKey.length/4-2,w=4,A=0;A<v;A++)r=o[f>>>24]^s[u>>16&255]^c[g>>8&255]^y[255&p]^this.decKey[w],n=o[u>>>24]^s[g>>16&255]^c[p>>8&255]^y[255&f]^this.decKey[w+1],i=o[g>>>24]^s[p>>16&255]^c[f>>8&255]^y[255&u]^this.decKey[w+2],p=o[p>>>24]^s[f>>16&255]^c[u>>8&255]^y[255&g]^this.decKey[w+3],f=r,u=n,g=i,w+=4;for(A=0;A<4;A++)l[3&-A]=e[f>>>24]<<24^e[u>>16&255]<<16^e[g>>8&255]<<8^e[255&p]^this.decKey[w++],r=f,f=u,u=g,g=p,p=r;return l},l}(),u=function(){function t(t,e,r){void 0===r&&(r=16),this.key=a(t),this.iv=a(e),this.cipher=new f(t),this.blockSize=r/4}return t.prototype.encrypt=function(t,e){for(var r=a(t),n=e||new Uint32Array(r.length),i=this.iv.subarray(this.blockSize,this.iv.length),h=this.iv.subarray(0,this.blockSize),o=new Uint32Array(this.blockSize),s=0;s<r.length;s+=this.blockSize){var c=r.subarray(s,s+this.blockSize);l(c,h,o);var y=this.cipher.encrypt(o);l(y,i),i=c,h=y;for(var f=s,u=0;f<r.length&&u<4;f++,u++)n[f]=y[u]}return n},t.prototype.decrypt=function(t,e){for(var r=a(t),n=e||new Uint32Array(r.length),i=this.iv.subarray(this.blockSize,this.iv.length),h=this.iv.subarray(0,this.blockSize),o=new Uint32Array(this.blockSize),s=0;s<n.length;s+=this.blockSize){var c=r.subarray(s,s+this.blockSize);l(c,i,o);var y=this.cipher.decrypt(o);l(y,h),h=c,i=y;for(var f=s,u=0;f<n.length&&u<4;f++,u++)n[f]=y[u]}return n},t}(),g=function(){function t(t,e,r){if(void 0===r&&(r=16),this.offset=0,this.key=a(t),this.counter=a(e),this.cipher=new f(t),this.blockSize=r/4,4!==this.counter.length)throw new Error("AES-CTR mode counter must be 16 bytes length")}return t.prototype.encrypt=function(t,e){for(var r=a(t),n=e||new Uint32Array(r.length),i=this.offset,h=0;h<r.length;h+=this.blockSize){for(var o=this.cipher.encrypt(this.counter),s=h,c=i;s<r.length&&c<this.blockSize;s++,c++)n[s]=o[c]^r[s];r.length-h>=this.blockSize&&this.incrementCounter(),i&&(h-=i,i=0)}return this.offset=(this.offset+r.length%4)%4,n},t.prototype.decrypt=function(t,e){return this.encrypt(t,e)},t.prototype.incrementCounter=function(){for(var t=this.counter.length-1;t>=0&&!(++this.counter[t]<4294967295);t--);},t}();exports.CTR=g,exports.IGE=u,exports.default=f;
